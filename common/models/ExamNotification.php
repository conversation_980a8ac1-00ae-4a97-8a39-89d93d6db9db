<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "exam_notification".
 *
 * @property int $id
 * @property int|null $exam_id
 * @property int|null $stream_id
 * @property int|null $course_id
 * @property int|null $program_id
 * @property string|null $text
 * @property string|null $content
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $publish_at
 *
 * @property Course $course
 * @property Exam $exam
 * @property Program $program
 * @property Stream $stream
 */
class ExamNotification extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'exam_notification';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['exam_id', 'course_id', 'program_id', 'stream_id', 'status'], 'integer'],
            [['content'], 'string'],
            [['created_at', 'updated_at', 'start_date', 'end_date', 'publish_at'], 'safe'],
            [['text', 'content', 'start_date', 'end_date'], 'required'],
            [['text'], 'string', 'max' => 255],
            [['course_id'], 'exist', 'skipOnError' => true, 'targetClass' => Course::className(), 'targetAttribute' => ['course_id' => 'id']],
            [['exam_id'], 'exist', 'skipOnError' => true, 'targetClass' => Exam::className(), 'targetAttribute' => ['exam_id' => 'id']],
            [['program_id'], 'exist', 'skipOnError' => true, 'targetClass' => Program::className(), 'targetAttribute' => ['program_id' => 'id']],
            [['stream_id'], 'exist', 'skipOnError' => true, 'targetClass' => Stream::className(), 'targetAttribute' => ['stream_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'exam_id' => 'Exam',
            'stream_id' => 'Stream',
            'course_id' => 'Course',
            'program_id' => 'Program',
            'text' => 'Text',
            'content' => 'Content',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'publish_at' => 'Publish At',
        ];
    }

    /**
     * Gets query for [[Course]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CourseQuery
     */
    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'course_id']);
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'exam_id']);
    }

    /**
     * Gets query for [[Program]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ProgramQuery
     */
    public function getProgram()
    {
        return $this->hasOne(Program::className(), ['id' => 'program_id']);
    }

    /**
     * Gets query for [[Stream]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StreamQuery
     */
    public function getStream()
    {
        return $this->hasOne(Stream::className(), ['id' => 'stream_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\ExamNotificationQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\ExamNotificationQuery(get_called_class());
    }
}
