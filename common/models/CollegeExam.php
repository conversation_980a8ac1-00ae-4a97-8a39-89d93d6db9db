<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "college_exam".
 *
 * @property int $college_id
 * @property int $exam_id
 *
 * @property College $college
 * @property Exam $exam
 */
class CollegeExam extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_exam';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'exam_id'], 'required'],
            [['college_id', 'exam_id'], 'integer'],
            [['college_id', 'exam_id'], 'unique', 'targetAttribute' => ['college_id', 'exam_id']],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['college_id' => 'id']],
            [['exam_id'], 'exist', 'skipOnError' => true, 'targetClass' => Exam::className(), 'targetAttribute' => ['exam_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'college_id' => 'College ID',
            'exam_id' => 'Exam ID',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'exam_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeExamQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeExamQuery(get_called_class());
    }
}
