<?php

namespace backend\controllers;

use Yii;
use common\models\ExamNotification;
use common\models\ProgramCourseMapping;
use common\models\ExamCourse;
use common\models\CollegeNotificationUpdate;
use common\models\College;
use common\models\CollegeExam;
use common\models\CollegeProgram;
use common\models\CollegeProgramExam;
use backend\models\ExamNotificationSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ExamNotificationController implements the CRUD actions for ExamNotification model.
 */
class ExamNotificationController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ExamNotification models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ExamNotificationSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ExamNotification model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Creates a new ExamNotification model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ExamNotification();
        $postData = Yii::$app->request->post();

        if ($model->load($postData)) {
            $examIds = array_filter((array) $model->exam_id);
            $courseIds = array_filter((array) $model->course_id);

            // Validate that either exam or course is selected
            if (empty($examIds) && empty($courseIds)) {
                $model->addError('course_id', 'Either Course or Exam must be selected.');
                $model->addError('exam_id', 'Either Course or Exam must be selected.');
            } else {
                $result = $this->processExamNotificationCreation($model, $postData);

                if ($result['success']) {
                    Yii::$app->session->set('savedRecords', $result['savedRecords']);
                    return $this->redirect(['view', 'id' => $result['modelId']]);
                }
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }
    /**
     * Updates an existing ExamNotification model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postData = Yii::$app->request->post();

        if ($model->load($postData)) {
            // For update, we handle single values only (no multiple selection in update mode)
            if ($model->status == 1 && $model->start_date == new \yii\db\Expression('NOW()')) {
                $model->publish_at = new \yii\db\Expression('NOW()');
            } else {
                $model->publish_at = $model->start_date;
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ExamNotification model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Process exam notification creation with optimized logic
     * @param ExamNotification $model
     * @param array $postData
     * @return array
     */
    private function processExamNotificationCreation($model, $postData)
    {
        $courseIds = array_filter((array) $model->course_id);
        $programIds = array_filter((array) $model->program_id);
        $examIds = array_filter((array) $model->exam_id);

        // Determine what combinations to create
        $combinations = $this->getExamCourseProgramCombinations($examIds, $courseIds, $programIds);

        // Create records for all combinations
        $result = $this->createNotificationRecords($combinations, $postData);

        return $result;
    }

    /**
     * Get exam-course-program combinations based on selection
     * @param array $examIds
     * @param array $courseIds
     * @param array $programIds
     * @return array
     */
    private function getExamCourseProgramCombinations($examIds, $courseIds, $programIds)
    {
        $combinations = [];

        // If only exams are selected (no courses), create records with null course and program
        if (!empty($examIds) && empty($courseIds)) {
            foreach ($examIds as $examId) {
                $combinations[] = [
                    'exam_id' => $examId,
                    'course_id' => null,
                    'program_id' => null
                ];
            }
            return $combinations;
        }

        // If both exams and courses are selected, check exam-course mapping
        if (!empty($examIds) && !empty($courseIds)) {
            $validExamCourseCombinations = $this->getValidExamCourseCombinations($examIds, $courseIds);

            foreach ($validExamCourseCombinations as $examCourse) {
                $examId = $examCourse['exam_id'];
                $courseId = $examCourse['course_id'];

                // Get course-program combinations for this course
                $courseProgramCombinations = $this->getCourseProgramCombinations([$courseId], $programIds);

                foreach ($courseProgramCombinations as $courseProgram) {
                    $combinations[] = [
                        'exam_id' => $examId,
                        'course_id' => $courseProgram['course_id'],
                        'program_id' => $courseProgram['program_id']
                    ];
                }
            }

            // Handle exams that don't have valid course mappings
            $mappedExamIds = array_unique(array_column($validExamCourseCombinations, 'exam_id'));
            $unmappedExamIds = array_diff($examIds, $mappedExamIds);

            foreach ($unmappedExamIds as $examId) {
                $combinations[] = [
                    'exam_id' => $examId,
                    'course_id' => null,
                    'program_id' => null
                ];
            }

            return $combinations;
        }

        // If only courses are selected (no exams), use existing logic
        if (empty($examIds) && !empty($courseIds)) {
            $courseProgramCombinations = $this->getCourseProgramCombinations($courseIds, $programIds);

            foreach ($courseProgramCombinations as $courseProgram) {
                $combinations[] = [
                    'exam_id' => null,
                    'course_id' => $courseProgram['course_id'],
                    'program_id' => $courseProgram['program_id']
                ];
            }
            return $combinations;
        }

        // Fallback: return single record with all null values
        return [['exam_id' => null, 'course_id' => null, 'program_id' => null]];
    }

    /**
     * Get course-program combinations based on selection
     * @param array $courseIds
     * @param array $programIds
     * @return array
     */
    private function getCourseProgramCombinations($courseIds, $programIds)
    {
        // If no courses selected, return single record with null course_id and program_id
        if (empty($courseIds)) {
            return [['course_id' => null, 'program_id' => null]];
        }

        // If no programs selected, return courses with null program_id
        if (empty($programIds)) {
            return array_map(function ($courseId) {
                return ['course_id' => $courseId, 'program_id' => null];
            }, $courseIds);
        }

        // Get valid combinations from mapping table
        $validCombinations = $this->getValidCourseProgramCombinations($courseIds, $programIds);

        // Get courses that have valid programs
        $coursesWithPrograms = array_unique(array_column($validCombinations, 'course_id'));

        // Get courses that don't have valid programs
        $coursesWithoutPrograms = array_diff($courseIds, $coursesWithPrograms);

        // Add courses without programs (with null program_id)
        foreach ($coursesWithoutPrograms as $courseId) {
            $validCombinations[] = ['course_id' => $courseId, 'program_id' => null];
        }

        // If no valid combinations at all, fallback to all courses with null program_id
        if (empty($validCombinations)) {
            return array_map(function ($courseId) {
                return ['course_id' => $courseId, 'program_id' => null];
            }, $courseIds);
        }

        return $validCombinations;
    }

    /**
     * Get valid exam-course combinations from database
     * @param array $examIds
     * @param array $courseIds
     * @return array
     */
    private function getValidExamCourseCombinations($examIds, $courseIds)
    {
        return ExamCourse::find()
            ->select(['exam_id', 'course_id'])
            ->where(['exam_id' => $examIds, 'course_id' => $courseIds])
            ->asArray()
            ->all();
    }

    /**
     * Get valid course-program combinations from database
     * @param array $courseIds
     * @param array $programIds
     * @return array
     */
    private function getValidCourseProgramCombinations($courseIds, $programIds)
    {
        return ProgramCourseMapping::find()
            ->select(['course_id', 'program_id'])
            ->where(['course_id' => $courseIds, 'program_id' => $programIds])
            ->asArray()
            ->all();
    }

    /**
     * Create notification records for given combinations
     * @param array $combinations
     * @param array $postData
     * @return array
     */
    private function createNotificationRecords($combinations, $postData)
    {
        $savedRecords = [];
        $hasErrors = false;

        foreach ($combinations as $combination) {
            $newModel = new ExamNotification();
            $newModel->load($postData);
            $newModel->exam_id = $combination['exam_id'];
            $newModel->course_id = $combination['course_id'];
            $newModel->program_id = $combination['program_id'];

            if ($newModel->status == 1 && $newModel->start_date == new \yii\db\Expression('NOW()')) {
                $newModel->publish_at = new \yii\db\Expression('NOW()');
            } else {
                $newModel->publish_at = $newModel->start_date;
            }

            if ($newModel->validate() && $newModel->save()) {
                $savedRecords[] = $newModel->id;
            } else {
                $hasErrors = true;
                print_r($newModel->getErrors());
            }
        }

        return [
            'success' => !empty($savedRecords),
            'savedCount' => count($savedRecords),
            'hasErrors' => $hasErrors,
            'modelId' => $savedRecords[0] ?? '',
            'savedRecords' => $savedRecords
        ];
    }

    /**
     * Create college notifications from exam notification
     * @param integer $id
     * @return mixed
     */
    public function actionCreateCollegeNotifications($id)
    {
        $examNotification = $this->findModel($id);
        $postData = Yii::$app->request->post();
        $uploadMode = $postData['CollegeNotificationUpdate']['upload_type'] ?? 'single';

        // Check if this is a bulk upload first (before model validation)
        if ($uploadMode === 'bulk' && isset($_FILES['bulk_csv']) && !empty($_FILES['bulk_csv']['tmp_name'])) {
            $result = $this->processBulkUploadFromCsv($examNotification, $_FILES['bulk_csv']);
        } elseif ($uploadMode === 'bulk') {
            Yii::$app->session->setFlash('error', 'Bulk upload mode selected but no CSV file uploaded.');
            return $this->redirect(['view', 'id' => $id]);
        } elseif (isset($postData['CollegeNotificationUpdate'])) {
            // Manual selection mode
            $collegeIds = array_filter((array) $postData['CollegeNotificationUpdate']['college_id']);
            $subPages = array_filter((array) $postData['CollegeNotificationUpdate']['sub_page']);

            if (empty($collegeIds) || empty($subPages)) {
                Yii::$app->session->setFlash('error', 'Please select both colleges and sub-pages.');
                return $this->redirect(['view', 'id' => $id]);
            }

            $result = $this->createCollegeNotificationRecords($examNotification, $collegeIds, $subPages);
        } else {
            Yii::$app->session->setFlash('error', 'Invalid request.');
            return $this->redirect(['view', 'id' => $id]);
        }

        if (isset($result)) {
            if ($result['success'] && !$result['hasErrors']) {
                Yii::$app->session->setFlash('success', "Successfully created {$result['count']} college notification records.");
            } elseif ($result['success'] && $result['hasErrors']) {
                Yii::$app->session->setFlash('warning', "Successfully created {$result['count']}. " . (!empty($result['error']) ? 'Failed Records : ' . implode(', ', $result['error']) : ''));
            } else {
                // Handle both string and array error formats
                $errorMessage = is_array($result['error']) ? implode(', ', $result['error']) : $result['error'];
                Yii::$app->session->setFlash('error', $errorMessage);
            }
        }

        return $this->redirect(['view', 'id' => $id]);
    }

    /**
     * Process bulk upload from CSV file
     * @param ExamNotification $examNotification
     * @param array $csvFile
     * @return array
     */
    private function processBulkUploadFromCsv($examNotification, $csvFile)
    {
        $filePath = $csvFile['tmp_name'];

        if (!file_exists($filePath)) {
            return ['success' => false, 'count' => 0, 'hasErrors' => true, 'error' => 'File not found.'];
        }

        if (!$this->isValidCsvMime($filePath)) {
            Yii::$app->session->setFlash('error', 'Invalid CSV File - Mime Type Mismatch');
            return ['success' => false, 'count' => 0, 'hasErrors' => true, 'error' => 'Invalid CSV File - Mime Type Mismatch'];
        }

        $successCount = 0;
        $errorCount = 0;
        $failDetails = [];

        if (($handle = fopen($filePath, 'r')) == false) {
            return ['success' => false, 'count' => 0, 'hasErrors' => true, 'error' => 'Unable to open the file'];
        }

        // First pass: Count total records to validate limit
        $totalRecords = 0;
        $tempRow = 0;
        while (($data = fgetcsv($handle, 2000, ',')) !== false) {
            if ($tempRow == 0 || empty($data[0])) {
                $tempRow++;
                continue;
            }
            $totalRecords++;
            $tempRow++;
        }

        // Check if total records exceed 200
        if ($totalRecords > 200) {
            fclose($handle);
            return [
                'success' => false,
                'count' => 0,
                'hasErrors' => true,
                'error' => "CSV file contains {$totalRecords} records. Maximum allowed is 200 records. Please reduce the number of records and try again."
            ];
        }

        // Reset file pointer to beginning for actual processing
        rewind($handle);

        $row = 0;
        $processedRecords = 0;
        while (($data = fgetcsv($handle, 2000, ',')) !== false) {
            if ($row == 0 || empty($data[0])) {
                $row++;
                continue;
            }

            // Additional safety check during processing
            if ($processedRecords >= 200) {
                $failDetails[] = "Processing stopped at row {$row}: Maximum limit of 200 records reached.";
                break;
            }

            $collegeId = (int) ($data[0] ?? '');
            $slugFromCSV = trim($data[1] ?? '');

            $collegeModel = College::findOne($collegeId);
            if (!$collegeModel) {
                $failDetails[] = "Row {$row}: College with ID {$collegeId} not found.";
                $errorCount++;
                $row++;
                continue;
            }

            $validSubPages = array_map('strtolower', array_column($collegeModel->collegeContents, 'sub_page'));
            if (!in_array(strtolower($slugFromCSV), $validSubPages)) {
                $failDetails[] = "Row {$row}: Sub Page '{$slugFromCSV}' not found for College {$collegeId} || {$collegeModel->name}.";
                $errorCount++;
                $row++;
                continue;
            }

            // Check if the college is valid for the exam notification's exam/course combination
            $examIds = $examNotification->exam_id ? [$examNotification->exam_id] : [];
            $courseIds = $examNotification->course_id ? [$examNotification->course_id] : [];

            $examIds = array_filter($examIds);
            $courseIds = array_filter($courseIds);

            $validCollegeIds = [];

            // CASE 1: Both exam and course are given
            if (!empty($examIds) && !empty($courseIds)) {
                $validCollegeIds = CollegeProgramExam::find()
                    ->leftJoin('college_program', 'college_program_exam.college_program_id = college_program.id')
                    ->select('college_id')
                    ->distinct()
                    ->where(['exam_id' => $examIds, 'course_id' => $courseIds])
                    ->column();
            } elseif (empty($examIds) && !empty($courseIds)) {
                // CASE 2: Only course is given
                $validCollegeIds = CollegeProgram::find()
                    ->select('college_id')
                    ->distinct()
                    ->where(['course_id' => $courseIds])
                    ->column();
            } elseif (!empty($examIds) && empty($courseIds)) {
                // CASE 3: Only exam is given
                $validCollegeIds = CollegeExam::find()
                    ->select('college_id')
                    ->distinct()
                    ->where(['exam_id' => $examIds])
                    ->column();
            }

            // Check if current college is in the valid list
            if (!empty($validCollegeIds) && !in_array($collegeId, $validCollegeIds)) {
                $errorMessage = "Row {$row}: College {$collegeId} || {$collegeModel->name} is not tagged with ";
                if (!empty($examIds) && !empty($courseIds)) {
                    $errorMessage .= "Exam ID {$examNotification->exam_id} and Course ID {$examNotification->course_id}.";
                } elseif (!empty($courseIds)) {
                    $errorMessage .= "Course ID {$examNotification->course_id}.";
                } elseif (!empty($examIds)) {
                    $errorMessage .= "Exam ID {$examNotification->exam_id}.";
                }

                $failDetails[] = $errorMessage;
                $errorCount++;
                $row++;
                continue;
            }

            $collegeNotification = new CollegeNotificationUpdate();
            $collegeNotification->college_id = $collegeId;
            $collegeNotification->sub_page = $slugFromCSV;
            $collegeNotification->text = $examNotification->text;
            $collegeNotification->content = $examNotification->content;
            $collegeNotification->start_date = $examNotification->start_date;
            $collegeNotification->end_date = $examNotification->end_date;
            $collegeNotification->status = $examNotification->status;

            if ($collegeNotification->status == 1 && $collegeNotification->start_date == new \yii\db\Expression('NOW()')) {
                $collegeNotification->publish_at = new \yii\db\Expression('NOW()');
            } else {
                $collegeNotification->publish_at = $collegeNotification->start_date;
            }

            if ($collegeNotification->save()) {
                $successCount++;
            } else {
                $errorCount++;
                $errors = array_map(
                    function ($attr, $messages) {
                        $label = ucfirst(str_replace('_', ' ', $attr));
                        return $label . ': ' . implode(', ', $messages);
                    },
                    array_keys($collegeNotification->getErrors()),
                    $collegeNotification->getErrors()
                );
                $failDetails[] = "Row {$row}: " . implode(', ', $errors);
            }

            $processedRecords++;
            $row++;
        }

        fclose($handle);

        // Set flash messages for errors if any
        if ($errorCount > 0) {
            $list = '<ul><li>' . implode('</li><li>', $failDetails) . '</li></ul>';
            Yii::$app->session->setFlash('warning', "CSV Import completed with {$successCount} records imported.<br>Failed Records: {$errorCount}<br>{$list}");
        }

        return [
            'success' => $successCount > 0,
            'count' => $successCount,
            'hasErrors' => $errorCount > 0,
            'error' => $failDetails
        ];
    }

    /**
     * Create college notification records
     * @param ExamNotification $examNotification
     * @param array $collegeIds
     * @param array $subPages
     * @return array
     */
    private function createCollegeNotificationRecords($examNotification, $collegeIds, $subPages)
    {
        $savedCount = 0;
        $hasErrors = false;
        $error = [];

        foreach ($collegeIds as $collegeId) {
            // Validate college exists
            $college = College::findOne($collegeId);
            if (!$college) {
                $hasErrors = true;
                $error[] = "College with ID {$collegeId} not found.";
                continue;
            }

            // Get valid sub-pages for this college
            $validSubPages = array_map('strtolower', array_column($college->collegeContents, 'sub_page'));

            foreach ($subPages as $subPage) {
                // Check if sub-page is valid for this college
                if (!in_array(strtolower($subPage), $validSubPages)) {
                    $hasErrors = true;
                    $error[] = "Sub Page '{$subPage}' not found for College {$collegeId} || {$college->name}.";
                    continue;
                }

                $collegeNotification = new CollegeNotificationUpdate();
                $collegeNotification->college_id = $collegeId;
                $collegeNotification->sub_page = $subPage;
                $collegeNotification->text = $examNotification->text;
                $collegeNotification->content = $examNotification->content;
                $collegeNotification->start_date = $examNotification->start_date;
                $collegeNotification->end_date = $examNotification->end_date;
                $collegeNotification->status = $examNotification->status;

                if ($collegeNotification->status == 1 && $collegeNotification->start_date == new \yii\db\Expression('NOW()')) {
                    $collegeNotification->publish_at = new \yii\db\Expression('NOW()');
                } else {
                    $collegeNotification->publish_at = $collegeNotification->start_date;
                }

                if ($collegeNotification->save()) {
                    $savedCount++;
                } else {
                    $hasErrors = true;
                    $error[] = "Failed to create college notification record for College {$collegeId} || {$college->name}.";
                }
            }
        }

        return [
            'success' => $savedCount > 0,
            'count' => $savedCount,
            'hasErrors' => $hasErrors,
            'error' => $error
        ];
    }

    /**
     * Validate CSV file mime type
     * @param string $filePath
     * @return bool
     */
    private function isValidCsvMime($filePath)
    {
        $allowedMimes = [
            'text/x-comma-separated-values',
            'text/comma-separated-values',
            'application/octet-stream',
            'application/vnd.ms-excel',
            'application/x-csv',
            'text/x-csv',
            'text/csv',
            'application/csv',
            'application/excel',
            'application/vnd.msexcel',
            'text/plain',
            'text/html',
        ];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        return in_array($mime, $allowedMimes, true);
    }

    /**
     * Finds the ExamNotification model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ExamNotification the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ExamNotification::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
