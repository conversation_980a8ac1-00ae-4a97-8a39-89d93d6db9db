<?php

namespace backend\tests\unit;

use Codeception\Test\Unit;
use backend\controllers\ExamNotificationController;
use common\models\ExamNotification;
use common\models\College;
use common\models\CollegeExam;
use common\models\CollegeProgram;
use common\models\CollegeProgramExam;
use Yii;

class ExamNotificationCollegeTest extends Unit
{
    protected $controller;

    protected function _before()
    {
        $this->controller = new ExamNotificationController('exam-notification', Yii::$app);
    }

    public function testGetRelevantColleges_ExamOnly()
    {
        // Test case: Only exam selected
        $model = new ExamNotification();
        $model->exam_id = 1;
        $model->course_id = null;
        $model->program_id = null;

        $method = new \ReflectionMethod($this->controller, 'getRelevantColleges');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $model);

        // Should return array (empty or with colleges)
        $this->assertIsArray($result);
    }

    public function testGetRelevantColleges_CourseOnly()
    {
        // Test case: Only course selected
        $model = new ExamNotification();
        $model->exam_id = null;
        $model->course_id = 1;
        $model->program_id = null;

        $method = new \ReflectionMethod($this->controller, 'getRelevantColleges');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $model);

        // Should return array (empty or with colleges)
        $this->assertIsArray($result);
    }

    public function testGetRelevantColleges_ExamAndCourse()
    {
        // Test case: Both exam and course selected
        $model = new ExamNotification();
        $model->exam_id = 1;
        $model->course_id = 1;
        $model->program_id = null;

        $method = new \ReflectionMethod($this->controller, 'getRelevantColleges');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $model);

        // Should return array (empty or with colleges)
        $this->assertIsArray($result);
    }

    public function testGetRelevantColleges_NoExamOrCourse()
    {
        // Test case: Neither exam nor course selected
        $model = new ExamNotification();
        $model->exam_id = null;
        $model->course_id = null;
        $model->program_id = null;

        $method = new \ReflectionMethod($this->controller, 'getRelevantColleges');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $model);

        // Should return empty array
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function testGetCollegesForExam()
    {
        // Test the private method for getting colleges by exam
        $examId = 1;

        $method = new \ReflectionMethod($this->controller, 'getCollegesForExam');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $examId);

        // Should return array of college IDs
        $this->assertIsArray($result);
    }

    public function testGetCollegesForCourse()
    {
        // Test the private method for getting colleges by course
        $courseId = 1;

        $method = new \ReflectionMethod($this->controller, 'getCollegesForCourse');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $courseId);

        // Should return array of college IDs
        $this->assertIsArray($result);
    }

    public function testGetCollegesForExamAndCourse()
    {
        // Test the private method for getting colleges by exam and course
        $examId = 1;
        $courseId = 1;

        $method = new \ReflectionMethod($this->controller, 'getCollegesForExamAndCourse');
        $method->setAccessible(true);
        $result = $method->invoke($this->controller, $examId, $courseId);

        // Should return array of college IDs
        $this->assertIsArray($result);
    }
}
