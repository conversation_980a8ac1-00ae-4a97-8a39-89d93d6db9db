<?php

use common\helpers\CollegeHelper;
use frontend\helpers\Html;
use frontend\helpers\Url;
use kartik\depdrop\DepDrop;
use kartik\select2\Select2;
use yii\web\JsExpression;

$condition = ($type == 'college-notification-update' ? $model->isNewRecord : true);
$examNotificationId = ($type == 'college-notification-update' ? null : $examNotification->id);
?>

<?php if ($condition): ?>
    <!-- Upload Mode Toggle -->
    <div class="form-group">
        <label class="control-label">Upload Mode</label>
        <div class="btn-group btn-group-toggle" data-toggle="buttons">
            <label class="btn btn-outline-primary active" id="single-mode-btn">
                <input type="radio" name="CollegeNotificationUpdate[upload_type]" id="single_mode" value="single" checked> Manual Selection
            </label>
            <label class="btn btn-outline-primary" id="bulk-mode-btn">
                <input type="radio" name="CollegeNotificationUpdate[upload_type]" id="bulk_mode" value="bulk"> Bulk Upload
            </label>
        </div>
    </div>
<?php endif; ?>

<!-- Single Upload Fields -->
<div id="single-upload-fields">
    <?=
    $form->field($model, 'college_id')->widget(Select2::class, [
        'disabled' => !$model->isNewRecord,
        'data' => $data,
        'options' => [
            'placeholder' => '--Select--',
            'multiple' => $type == 'college-notification-update' ? false : true,
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'minimumInputLength' => $type == 'college-notification-update' ? 3 : 0,
            'language' => [
                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
            ],
            'ajax' => [
                'url' => [$ajax],
                'dataType' => 'json',
                'data' => new JsExpression('function(params) {return {q:params.term,  examNotificationId: ' . (int)$examNotificationId . '}; }'),
            ],
            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
            'templateResult' => new JsExpression('function(data) { return data.text; }'),
            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
        ],
        'pluginEvents' => [
            'change' => 'function(data) {
                    $.post( "' . Url::toRoute('college-content/check-college-page-combination') . '", { college: $(this).val(),page:$("#sub_page").find(":selected").val()}).done(function( data ) { if(data){
                        $(".sub-page-drpdown").show();
                    }else{
                        $(".sub-page-drpdown").hide();
                        $("#parent_id").val("").trigger("change");
                    }});
                }',
        ]
    ])->label('College Name');
    ?>

    <?= $form->field($model, 'sub_page')->widget(DepDrop::class, [
        'type' => DepDrop::TYPE_SELECT2,
        'options' => [
            'id' => 'sub_page',
            'placeholder' => '--Select Sub Page--',
            'multiple' => $type == 'college-notification-update' ? false : true,
        ],
        'select2Options' => ['pluginOptions' => [
            'allowClear' => true,
            'disabled' => !$model->isNewRecord,
        ]],
        'pluginOptions' => [
            'depends' => ['collegenotificationupdate-college_id'],
            'placeholder' => 'Select Sub Page...',
            'url' => Url::to(['/college-content/all-active-subpage'])
        ],

    ])->label('Sub Page');
?>
</div>


<!-- Bulk Upload Fields -->
<div id="bulk-upload-fields" style="display: none;">

    <div class="card-body">
        <p>Please ensure your CSV file follows the exact format below:</p>

        <div class="table-responsive">
            <table class="table table-bordered table-hover table-sm text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th>college_id</th>
                        <th>sub_page</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>520</td>
                        <td>info</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="my-3 alert well">
            <strong>College Sub Pages:</strong>
            <div>
                <?php foreach (array_keys(CollegeHelper::$subPages) as $key): ?>
                    <span class="subpage-box"><?= Html::encode($key) ?></span>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label">CSV File (College ID & Sub Page)</label>
        <?php if ($type == 'exam-notification'): ?>
            <div class="alert alert-warning" style="margin-bottom: 10px;">
                <strong>Important:</strong>
                <ul style="margin-bottom: 0; padding-left: 20px;">
                    <li>Maximum 200 records allowed per upload. Files with more than 200 records will be rejected.</li>
                    <?php if (isset($examNotification)): ?>
                        <li>Only colleges tagged with the selected
                            <?php if ($examNotification->exam_id && $examNotification->course_id): ?>
                                exam and course combination
                            <?php elseif ($examNotification->course_id): ?>
                                course
                            <?php elseif ($examNotification->exam_id): ?>
                                exam
                            <?php endif; ?>
                            will be processed. Untagged colleges will be marked as failed records.
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        <?php endif; ?>
        <div class="dropzone-wrapper">
            <div class="dropzone-desc">
                <i class="glyphicon glyphicon-download-alt"></i>
                <p>Choose a CSV file or drag it here.</p>
                <p><small>CSV should contain: college_id, sub_page</small></p>
            </div>
            <input type="file" name="bulk_csv" class="dropzone" accept=".csv" />
        </div>
    </div>
</div>

<?php
$script = <<<JS
$(document).ready(function() {
    // Toggle between single and bulk upload modes
$('input[name="CollegeNotificationUpdate[upload_type]"]').change(function () {
    var mode = $(this).val();
    console.log(mode);

    if (mode === 'single') {
        $('#single-upload-fields').show();
        $('#bulk-upload-fields').hide();
        $('#single-mode-btn').addClass('active');
        $('#bulk-mode-btn').removeClass('active');
    } else {
        $('#single-upload-fields').hide();
        $('#bulk-upload-fields').show();
        $('#bulk-mode-btn').addClass('active');
        $('#single-mode-btn').removeClass('active');
    }
});

    // File drop functionality
    $('.dropzone').on('change', function() {
        var fileName = $(this)[0].files[0] ? $(this)[0].files[0].name : '';
        if (fileName) {
            $(this).siblings('.dropzone-desc').html('<i class="glyphicon glyphicon-ok"></i><p>File selected: ' + fileName + '</p>');
        }
    });

    // Drag and drop events
    $('.dropzone-wrapper').on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('dragover');
    });

    $('.dropzone-wrapper').on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
    });

    $('.dropzone-wrapper').on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');

        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $(this).find('.dropzone')[0].files = files;
            $(this).find('.dropzone').trigger('change');
        }
    });

    document.getElementById('csvUploadForm').addEventListener('submit', function() {
    const progressBar = document.getElementById('uploadProgressBar');
    const progress = document.getElementById('uploadProgress');
    progressBar.classList.remove('d-none');
    let width = 0;
    const interval = setInterval(function() {
        if (width >= 100) {
            clearInterval(interval);
        } else {
            width += 10;
            progress.style.width = width + '%';
        }
    }, 300);
});
});
JS;
$this->registerJs($script);
?>